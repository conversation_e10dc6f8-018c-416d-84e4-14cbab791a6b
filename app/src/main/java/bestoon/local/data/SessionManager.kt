package bestoon.local.data

import android.content.Context
import android.content.SharedPreferences
import bestoon.local.data.model.User
import bestoon.local.utils.BiometricCredentialManager
import com.google.gson.Gson

/**
 * Manages user session data including authentication token and user information.
 */
class SessionManager(context: Context) {

    private var prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    private val biometricCredentialManager = BiometricCredentialManager(context)

    companion object {
        private const val PREF_NAME = "ArielAppPrefs"
        private const val KEY_TOKEN = "user_token"
        private const val KEY_USER = "user_data"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_BIOMETRIC_ENABLED = "biometric_enabled"
        private const val KEY_CHOSEN_YEAR = "chosen_year"
        private const val KEY_CHOSEN_MONTH_START = "chosen_month_start"
        private const val KEY_CHOSEN_MONTH_END = "chosen_month_end"
    }

    /**
     * Saves the authentication token to SharedPreferences.
     *
     * @param token The authentication token to save.
     */
    fun saveAuthToken(token: String) {
        val editor = prefs.edit()
        editor.putString(KEY_TOKEN, token)
        editor.apply()
    }

    /**
     * Retrieves the saved authentication token.
     *
     * @return The saved token or null if not found.
     */
    fun getAuthToken(): String? {
        return prefs.getString(KEY_TOKEN, null)
    }

    /**
     * Saves user information to SharedPreferences.
     *
     * @param user The User object to save.
     */
    fun saveUser(user: User) {
        val userJson = gson.toJson(user)
        val editor = prefs.edit()
        editor.putString(KEY_USER, userJson)
        editor.apply()
    }

    /**
     * Retrieves the saved user information.
     *
     * @return The User object or null if not found.
     */
    fun getUser(): User? {
        val userJson = prefs.getString(KEY_USER, null) ?: return null
        return try {
            gson.fromJson(userJson, User::class.java)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Sets the logged-in status of the user.
     *
     * @param isLoggedIn Whether the user is logged in.
     */
    fun setLoggedIn(isLoggedIn: Boolean) {
        val editor = prefs.edit()
        editor.putBoolean(KEY_IS_LOGGED_IN, isLoggedIn)
        editor.apply()
    }

    /**
     * Checks if the user is logged in.
     *
     * @return True if the user is logged in, false otherwise.
     */
    fun isLoggedIn(): Boolean {
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false)
    }

    /**
     * Clears all session data when the user logs out.
     * Preserves biometric credentials and settings for future logins.
     */
    fun clearSession() {
        // Save biometric enabled state before clearing
        val biometricEnabled = isBiometricEnabled()

        val editor = prefs.edit()
        editor.clear()

        // Restore biometric enabled state after clearing
        // Credentials are preserved in the secure BiometricCredentialManager
        if (biometricEnabled) {
            editor.putBoolean(KEY_BIOMETRIC_ENABLED, biometricEnabled)
        }

        editor.apply()
    }

    /**
     * Checks if the current user is a superuser.
     *
     * @return True if the user is a superuser, false otherwise.
     */
    fun getUserIsSuperuser(): Boolean {
        return getUser()?.isSuperuser ?: false
    }

    /**
     * Checks if the current user is a limited admin.
     *
     * @return True if the user is a limited admin, false otherwise.
     */
    fun getUserIsLimitedAdmin(): Boolean {
        return getUser()?.isLimitedAdmin ?: false
    }

    /**
     * Enables or disables biometric authentication for the user.
     *
     * @param enabled Whether biometric authentication should be enabled.
     */
    fun setBiometricEnabled(enabled: Boolean) {
        val editor = prefs.edit()
        editor.putBoolean(KEY_BIOMETRIC_ENABLED, enabled)
        editor.apply()
    }

    /**
     * Checks if biometric authentication is enabled for the user.
     *
     * @return True if biometric authentication is enabled, false otherwise.
     */
    fun isBiometricEnabled(): Boolean {
        return prefs.getBoolean(KEY_BIOMETRIC_ENABLED, false)
    }

    /**
     * Saves user credentials for biometric authentication securely.
     *
     * @param username The username to save.
     * @param password The password to save.
     * @return True if credentials were saved successfully, false otherwise.
     */
    fun saveBiometricCredentials(username: String, password: String): Boolean {
        return biometricCredentialManager.saveCredentials(username, password)
    }

    /**
     * Retrieves saved biometric credentials securely.
     *
     * @return A pair of username and password, or null if not found.
     */
    fun getBiometricCredentials(): Pair<String, String>? {
        return biometricCredentialManager.getCredentials()
    }

    /**
     * Clears biometric authentication data.
     */
    fun clearBiometricData() {
        val editor = prefs.edit()
        editor.remove(KEY_BIOMETRIC_ENABLED)
        editor.apply()
        biometricCredentialManager.clearCredentials()
    }

    /**
     * Saves the chosen year filter parameter.
     *
     * @param year The chosen year to save.
     */
    fun saveChosenYear(year: Int) {
        val editor = prefs.edit()
        editor.putInt(KEY_CHOSEN_YEAR, year)
        editor.apply()
    }

    /**
     * Retrieves the saved chosen year filter parameter.
     *
     * @return The saved chosen year or null if not found.
     */
    fun getChosenYear(): Int? {
        return if (prefs.contains(KEY_CHOSEN_YEAR)) {
            prefs.getInt(KEY_CHOSEN_YEAR, 0)
        } else {
            null
        }
    }

    /**
     * Saves the chosen month start filter parameter.
     *
     * @param monthStart The chosen month start to save.
     */
    fun saveChosenMonthStart(monthStart: Int) {
        val editor = prefs.edit()
        editor.putInt(KEY_CHOSEN_MONTH_START, monthStart)
        editor.apply()
    }

    /**
     * Retrieves the saved chosen month start filter parameter.
     *
     * @return The saved chosen month start or null if not found.
     */
    fun getChosenMonthStart(): Int? {
        return if (prefs.contains(KEY_CHOSEN_MONTH_START)) {
            prefs.getInt(KEY_CHOSEN_MONTH_START, 0)
        } else {
            null
        }
    }

    /**
     * Saves the chosen month end filter parameter.
     *
     * @param monthEnd The chosen month end to save.
     */
    fun saveChosenMonthEnd(monthEnd: Int) {
        val editor = prefs.edit()
        editor.putInt(KEY_CHOSEN_MONTH_END, monthEnd)
        editor.apply()
    }

    /**
     * Retrieves the saved chosen month end filter parameter.
     *
     * @return The saved chosen month end or null if not found.
     */
    fun getChosenMonthEnd(): Int? {
        return if (prefs.contains(KEY_CHOSEN_MONTH_END)) {
            prefs.getInt(KEY_CHOSEN_MONTH_END, 0)
        } else {
            null
        }
    }

    /**
     * Clears the chosen month end filter parameter.
     */
    fun clearChosenMonthEnd() {
        val editor = prefs.edit()
        editor.remove(KEY_CHOSEN_MONTH_END)
        editor.apply()
    }
}
