package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.SearchResult
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles search-related operations.
 */
class SearchRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    suspend fun search(token: String, query: String): Result<SearchResult> {
        return safeApiCall {
            apiService.search(TokenUtils.formatToken(token), query)
        }
    }
}
