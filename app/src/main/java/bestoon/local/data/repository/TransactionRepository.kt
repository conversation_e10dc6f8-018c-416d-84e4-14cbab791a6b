package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.BankResponse
import ir.rahavardit.ariel.data.model.NewTransactionRequest
import ir.rahavardit.ariel.data.model.TagResponse
import ir.rahavardit.ariel.data.model.TransactionCategoryResponse
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles transaction-related operations.
 */
class TransactionRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of transaction modes.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of transaction modes or an Exception.
     */
    suspend fun getTransactionModes(token: String): Result<List<TransactionMode>> {
        return safeApiCall {
            apiService.getTransactionModes(TokenUtils.formatToken(token))
        }
    }

    /**
     * Retrieves the list of banks.
     *
     * @param token The authentication token.
     * @return A Result containing either the bank response or an Exception.
     */
    suspend fun getBanks(token: String): Result<BankResponse> {
        return safeApiCall {
            apiService.getBanks(TokenUtils.formatToken(token))
        }
    }

    /**
     * Retrieves the list of categories for transactions.
     *
     * @param token The authentication token.
     * @return A Result containing either the category response or an Exception.
     */
    suspend fun getTransactionCategories(token: String): Result<TransactionCategoryResponse> {
        return safeApiCall {
            apiService.getTransactionCategories(TokenUtils.formatToken(token))
        }
    }

    /**
     * Retrieves the list of tags.
     *
     * @param token The authentication token.
     * @return A Result containing either the tag response or an Exception.
     */
    suspend fun getTags(token: String): Result<TagResponse> {
        return safeApiCall {
            apiService.getTags(TokenUtils.formatToken(token))
        }
    }

    /**
     * Creates a new transaction.
     *
     * @param token The authentication token.
     * @param request The new transaction request data.
     * @return A Result containing either the created transaction or an Exception.
     */
    suspend fun createTransaction(token: String, request: NewTransactionRequest): Result<TransactionResponse> {
        return safeApiCall {
            apiService.createTransaction(TokenUtils.formatToken(token), request)
        }
    }

    /**
     * Updates a transaction by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the transaction to update.
     * @param request The updated transaction data.
     * @return A Result containing either the updated transaction or an Exception.
     */
    suspend fun updateTransaction(token: String, shortUuid: String, request: NewTransactionRequest): Result<TransactionResponse> {
        return safeApiCall {
            apiService.updateTransaction(TokenUtils.formatToken(token), shortUuid, request)
        }
    }

    /**
     * Deletes a transaction by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the transaction to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteTransaction(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteTransaction(TokenUtils.formatToken(token), shortUuid)
        }
    }
}
