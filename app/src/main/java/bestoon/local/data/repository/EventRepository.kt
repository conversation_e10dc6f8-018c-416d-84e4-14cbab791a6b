package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.NewEventRequest
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles event-related operations.
 */
class EventRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Updates an event by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event to update.
     * @param request The updated event data.
     * @return A Result indicating success or failure.
     */
    suspend fun updateEvent(token: String, shortUuid: String, request: NewEventRequest): Result<Unit> {
        return safeApiCallUnit {
            apiService.updateEvent(TokenUtils.formatToken(token), shortUuid, request)
        }
    }

    /**
     * Deletes an event by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the event to delete.
     * @return A Result indicating success or failure.
     */
    suspend fun deleteEvent(token: String, shortUuid: String): Result<Unit> {
        return safeApiCallUnit {
            apiService.deleteEvent(TokenUtils.formatToken(token), shortUuid)
        }
    }
}
