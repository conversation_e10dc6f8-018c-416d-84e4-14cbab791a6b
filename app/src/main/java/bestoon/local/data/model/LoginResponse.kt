package ir.rahavardit.ariel.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the response from the login API.
 *
 * @property token The authentication token returned by the API.
 * @property id The user's unique identifier.
 * @property username The username of the authenticated user.
 * @property isSuperuser Whether the user has superuser privileges.
 * @property isLimitedAdmin Whether the user has limited admin privileges.
 */
data class LoginResponse(
    val token: String,
    val id: Int,
    val username: String,
    @SerializedName("is_superuser") val isSuperuser: <PERSON><PERSON><PERSON>,
    @SerializedName("is_limited_admin") val isLimitedAdmin: <PERSON><PERSON>an
)
