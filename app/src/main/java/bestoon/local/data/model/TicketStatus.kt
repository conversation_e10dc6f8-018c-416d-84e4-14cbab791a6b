package ir.rahavardit.ariel.data.model

/**
 * Object containing constants for ticket status codes.
 */
object TicketStatus {
    // Status codes as provided
    const val PENDING = "P"      // Pending
    const val IN_PROGRESS = "I"  // In progress
    const val RESPONDED = "R"    // Responded
    const val CLOSED = "C"       // Closed
    const val DISABLED = "D"     // Disabled

    /**
     * Checks if the ticket status is closed or disabled, meaning it cannot be modified.
     *
     * @param status The status code to check.
     * @return True if the ticket is closed or disabled and cannot be modified, false otherwise.
     */
    fun isDisabledOrClosed(status: String?): <PERSON><PERSON><PERSON> {
        return status == DISABLED || status == CLOSED
    }
}
