package ir.rahavardit.ariel.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Data class representing the new homepage statistics response from the API.
 */
data class HomepageDataResponse(
    val years: List<Int>,
    @SerializedName("names_of_months__persian")
    val namesOfMonthsPersian: List<String>,
    val chosenyear: Int,
    val chosenmonthstart: Int,
    val chosenmonthend: Int,
    @SerializedName("chosenmonthstart__zeroed")
    val chosenmonthstartZeroed: String,
    @SerializedName("chosenmonthend__zeroed")
    val chosenmonthendZeroed: String,
    @SerializedName("chosenyear__persian")
    val chosenyearPersian: String,
    @SerializedName("chosenmonthstart__zeroed__persian")
    val chosenmonthstartZeroedPersian: String,
    @SerializedName("chosenmonthend__zeroed__persian")
    val chosenmonthendZeroedPersian: String,
    @SerializedName("income_objects")
    val incomeObjects: List<IncomeObject>,
    @SerializedName("expenditure_objects")
    val expenditureObjects: List<ExpenditureObject>,
    @SerializedName("event_objects")
    val eventObjects: List<EventObject>
)

/**
 * Data class representing an income object.
 */
@Parcelize
data class IncomeObject(
    val id: Int,
    val mode: String,
    val title: String?,
    val author: Author?,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    @SerializedName("bank_info")
    val bankInfo: Bank?,
    @SerializedName("category_info")
    val categoryInfo: HomepageCategory?,
    val tags: List<Int>?,
    @SerializedName("tags_names")
    val tagsNames: List<String>?,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    val updated: String
) : Parcelable

/**
 * Data class representing an expenditure object.
 */
@Parcelize
data class ExpenditureObject(
    val id: Int,
    val mode: String,
    val title: String?,
    val author: Author?,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    @SerializedName("bank_info")
    val bankInfo: Bank?,
    @SerializedName("category_info")
    val categoryInfo: HomepageCategory?,
    val tags: List<Int>?,
    @SerializedName("tags_names")
    val tagsNames: List<String>?,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    val updated: String
) : Parcelable

/**
 * Data class representing an event object.
 */
@Parcelize
data class EventObject(
    val id: Int,
    val title: String,
    val author: Author?,
    val year: Int,
    val month: Int,
    val day: Int,
    @SerializedName("slashed_date")
    val slashedDate: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    @SerializedName("numerical_date")
    val numericalDate: Int,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    val updated: String
) : Parcelable

/**
 * Data class representing a bank.
 */
@Parcelize
data class Bank(
    val id: Int,
    val title: String,
    @SerializedName("short_uuid")
    val shortUuid: String
) : Parcelable

/**
 * Data class representing a category for homepage objects.
 */
@Parcelize
data class HomepageCategory(
    val id: Int,
    val title: String,
    @SerializedName("short_uuid")
    val shortUuid: String
) : Parcelable
