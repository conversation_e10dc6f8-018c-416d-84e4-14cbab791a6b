package ir.rahavardit.ariel.data.model

/**
 * Data class representing a user in the system.
 *
 * @property id The unique identifier for the user.
 * @property username The username of the user.
 * @property isSuperuser Whether the user has superuser privileges.
 * @property isLimitedAdmin Whether the user has limited admin privileges.
 */
data class User(
    val id: Int,
    val username: String,
    val isSuperuser: <PERSON><PERSON><PERSON>,
    val isLimitedAdmin: <PERSON><PERSON><PERSON>
)
