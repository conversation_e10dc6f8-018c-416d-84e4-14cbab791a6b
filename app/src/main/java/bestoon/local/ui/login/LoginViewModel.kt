package ir.rahavardit.ariel.ui.login

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.LoginResponse
import ir.rahavardit.ariel.data.model.User
import ir.rahavardit.ariel.data.repository.AuthRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the login screen that handles authentication logic.
 */
class LoginViewModel : ViewModel() {
    
    private val authRepository = AuthRepository()
    
    private val _loginResult = MutableLiveData<LoginResult>()
    val loginResult: LiveData<LoginResult> = _loginResult
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    /**
     * Attempts to log in with the provided credentials.
     *
     * @param username The username for authentication.
     * @param password The password for authentication.
     */
    fun login(username: String, password: String) {
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val result = authRepository.login(username, password)
                
                result.fold(
                    onSuccess = { loginResponse ->
                        val user = authRepository.getUserFromLoginResponse(loginResponse)
                        _loginResult.value = LoginResult.Success(loginResponse, user)
                    },
                    onFailure = { exception ->
                        _loginResult.value = LoginResult.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _loginResult.value = LoginResult.Error(e.message ?: "Unknown error occurred")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Validates the login form inputs.
     *
     * @param username The username to validate.
     * @param password The password to validate.
     * @return A pair of booleans indicating if the username and password are valid.
     */
    fun validateInputs(username: String, password: String): Pair<Boolean, Boolean> {
        val isUsernameValid = username.isNotBlank()
        val isPasswordValid = password.isNotBlank() && password.length >= 4
        
        return Pair(isUsernameValid, isPasswordValid)
    }
    
    /**
     * Sealed class representing the result of a login attempt.
     */
    sealed class LoginResult {
        /**
         * Represents a successful login.
         *
         * @property loginResponse The response from the login API.
         * @property user The authenticated user.
         */
        data class Success(val loginResponse: LoginResponse, val user: User) : LoginResult()
        
        /**
         * Represents a failed login.
         *
         * @property errorMessage The error message describing why the login failed.
         */
        data class Error(val errorMessage: String) : LoginResult()
    }
}
