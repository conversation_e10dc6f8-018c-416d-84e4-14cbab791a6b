package ir.rahavardit.ariel.ui.newevent

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.NewEventRequest
import ir.rahavardit.ariel.data.repository.NewEventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the New Event screen.
 */
class NewEventViewModel(private val token: String) : ViewModel() {

    private val repository = NewEventRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _createEventResult = MutableLiveData<Result<Unit>>()
    val createEventResult: LiveData<Result<Unit>> = _createEventResult

    /**
     * Creates a new event.
     *
     * @param title The title of the event.
     * @param date The date of the event in YYYY/MM/DD format (English numerals).
     */
    fun createEvent(title: String, date: String) {
        _isLoading.value = true

        viewModelScope.launch {
            try {
                val request = NewEventRequest(
                    title = title,
                    date = date
                )

                val result = repository.createEvent(token, request)
                _createEventResult.value = result
            } catch (e: Exception) {
                _createEventResult.value = Result.failure(e)
            } finally {
                _isLoading.value = false
            }
        }
    }
}
