package ir.rahavardit.ariel.ui.newtransaction

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.BankItem
import ir.rahavardit.ariel.data.model.NewTransactionRequest
import ir.rahavardit.ariel.data.model.TagItem
import ir.rahavardit.ariel.data.model.TransactionCategoryItem
import ir.rahavardit.ariel.data.model.TransactionMode
import ir.rahavardit.ariel.data.model.TransactionResponse
import ir.rahavardit.ariel.data.repository.TransactionRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the NewTransactionFragment.
 */
class NewTransactionViewModel : ViewModel() {

    private val transactionRepository = TransactionRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _transactionModes = MutableLiveData<List<TransactionMode>>()
    val transactionModes: LiveData<List<TransactionMode>> = _transactionModes

    private val _banks = MutableLiveData<List<BankItem>>()
    val banks: LiveData<List<BankItem>> = _banks

    private val _categories = MutableLiveData<List<TransactionCategoryItem>>()
    val categories: LiveData<List<TransactionCategoryItem>> = _categories

    private val _tags = MutableLiveData<List<TagItem>>()
    val tags: LiveData<List<TagItem>> = _tags

    private val _transactionCreationResult = MutableLiveData<TransactionCreationResult>()
    val transactionCreationResult: LiveData<TransactionCreationResult> = _transactionCreationResult

    /**
     * Sealed class representing the result of transaction creation.
     */
    sealed class TransactionCreationResult {
        data class Success(val transaction: TransactionResponse) : TransactionCreationResult()
        data class Error(val errorMessage: String) : TransactionCreationResult()
    }

    /**
     * Data class for input validation results.
     */
    data class ValidationResult(
        val isDateValid: Boolean,
        val isAmountValid: Boolean,
        val isModeValid: Boolean,
        val isCategoryValid: Boolean
    )

    /**
     * Loads transaction modes from the API.
     *
     * @param token The authentication token.
     */
    fun loadTransactionModes(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = transactionRepository.getTransactionModes(token)

                result.fold(
                    onSuccess = { modes ->
                        _transactionModes.value = modes
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load transaction modes"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load transaction modes"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads banks from the API.
     *
     * @param token The authentication token.
     */
    fun loadBanks(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getBanks(token)

                result.fold(
                    onSuccess = { response ->
                        _banks.value = response.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load banks"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load banks"
            }
        }
    }

    /**
     * Loads categories from the API.
     *
     * @param token The authentication token.
     */
    fun loadCategories(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTransactionCategories(token)

                result.fold(
                    onSuccess = { response ->
                        _categories.value = response.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load categories"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load categories"
            }
        }
    }

    /**
     * Loads tags from the API.
     *
     * @param token The authentication token.
     */
    fun loadTags(token: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getTags(token)

                result.fold(
                    onSuccess = { response ->
                        _tags.value = response.results
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to load tags"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to load tags"
            }
        }
    }

    /**
     * Validates the input fields.
     *
     * @param date The selected date.
     * @param amount The entered amount.
     * @param selectedMode The selected mode.
     * @param selectedCategory The selected category.
     * @return ValidationResult containing validation status for each field.
     */
    fun validateInputs(
        date: String?,
        amount: String,
        selectedMode: String?,
        selectedCategory: String?
    ): ValidationResult {
        return ValidationResult(
            isDateValid = !date.isNullOrBlank(),
            isAmountValid = amount.isNotBlank() && amount.toIntOrNull() != null && amount.toInt() > 0,
            isModeValid = !selectedMode.isNullOrBlank(),
            isCategoryValid = !selectedCategory.isNullOrBlank()
        )
    }

    /**
     * Creates a new transaction.
     *
     * @param token The authentication token.
     * @param date The transaction date in YYYY/MM/DD format.
     * @param title The transaction title (optional).
     * @param amount The transaction amount.
     * @param mode The transaction mode value.
     * @param bankId The bank ID (optional).
     * @param categoryId The category ID.
     * @param tagIds The list of tag IDs.
     */
    fun createTransaction(
        token: String,
        date: String,
        title: String?,
        amount: Int,
        mode: String,
        bankId: Int?,
        categoryId: Int,
        tagIds: List<Int>
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val request = NewTransactionRequest(
                    date = date,
                    title = if (title.isNullOrBlank()) null else title,
                    amount = amount,
                    mode = mode,
                    bank = bankId?.toString(),
                    category = categoryId.toString(),
                    tags = tagIds
                )

                val result = transactionRepository.createTransaction(token, request)

                result.fold(
                    onSuccess = { transaction ->
                        _transactionCreationResult.value = TransactionCreationResult.Success(transaction)
                    },
                    onFailure = { exception ->
                        _transactionCreationResult.value = TransactionCreationResult.Error(
                            exception.message ?: "Failed to create transaction"
                        )
                    }
                )
            } catch (e: Exception) {
                _transactionCreationResult.value = TransactionCreationResult.Error(
                    e.message ?: "Failed to create transaction"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }
}
