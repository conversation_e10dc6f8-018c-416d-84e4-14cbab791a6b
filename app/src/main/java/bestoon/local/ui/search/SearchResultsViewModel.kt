package ir.rahavardit.ariel.ui.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import ir.rahavardit.ariel.data.model.SearchResult
import ir.rahavardit.ariel.data.model.Ticket
import ir.rahavardit.ariel.data.repository.SearchRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the search results screen.
 */
class SearchResultsViewModel : ViewModel() {

    private val searchRepository = SearchRepository()

    private val _searchResults = MutableLiveData<SearchResult>()
    val searchResults: LiveData<SearchResult> = _searchResults

    private val _tickets = MutableLiveData<List<Ticket>>()
    val tickets: LiveData<List<Ticket>> = _tickets

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery

    /**
     * Performs a search with the given query.
     *
     * @param token The authentication token.
     * @param query The search query string.
     */
    fun search(token: String, query: String) {
        _isLoading.value = true
        _error.value = null
        _searchQuery.value = query

        viewModelScope.launch {
            val result = searchRepository.search(token, query)
            _isLoading.value = false

            result.fold(
                onSuccess = { searchResult ->
                    _searchResults.value = searchResult
                    _tickets.value = searchResult.matchedTickets
                },
                onFailure = { exception ->
                    _error.value = exception.message
                }
            )
        }
    }
}
