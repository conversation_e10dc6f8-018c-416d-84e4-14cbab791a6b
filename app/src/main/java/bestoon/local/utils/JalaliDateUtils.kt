package bestoon.local.utils

/**
 * Utility class for Jalali (Persian) calendar operations.
 */
object JalaliDateUtils {

    private val persianMonthNames = arrayOf(
        "فروردین", "اردیبهشت", "خرد<PERSON>", "تیر", "مرد<PERSON>", "شهریور",
        "مهر", "آبان", "آذر", "دی", "بهمن", "اسفند"
    )

    /**
     * Converts Gregorian date to Jalali date.
     *
     * @param gy Gregorian year
     * @param gm Gregorian month (1-12)
     * @param gd Gregorian day
     * @return IntArray containing [jalali_year, jalali_month, jalali_day]
     */
    fun gregorianToJalali(gy: Int, gm: Int, gd: Int): IntArray {
        // Simple conversion using known offset
        // March 22, 2025 = 1404/01/01 (approximately)

        // Calculate days since epoch (Jan 1, 1970)
        val gregorianEpoch = java.util.Calendar.getInstance().apply {
            set(gy, gm - 1, gd)
        }.timeInMillis / (24 * 60 * 60 * 1000)

        // March 22, 2025 is approximately 1404/01/01
        val jalaliEpoch = java.util.Calendar.getInstance().apply {
            set(2025, 2, 22) // March 22, 2025
        }.timeInMillis / (24 * 60 * 60 * 1000)

        val daysDiff = (gregorianEpoch - jalaliEpoch).toInt()

        // Start from 1404/01/01
        var jy = 1404
        var jm = 1
        var jd = 1 + daysDiff

        // Adjust for negative days (dates before 1404/01/01)
        while (jd <= 0) {
            jm--
            if (jm <= 0) {
                jm = 12
                jy--
            }
            jd += getDaysInJalaliMonth(jy, jm)
        }

        // Adjust for days exceeding month length
        while (jd > getDaysInJalaliMonth(jy, jm)) {
            jd -= getDaysInJalaliMonth(jy, jm)
            jm++
            if (jm > 12) {
                jm = 1
                jy++
            }
        }

        return intArrayOf(jy, jm, jd)
    }

    /**
     * Converts Jalali date to Gregorian date.
     *
     * @param jy Jalali year
     * @param jm Jalali month (1-12)
     * @param jd Jalali day
     * @return IntArray containing [gregorian_year, gregorian_month, gregorian_day]
     */
    fun jalaliToGregorian(jy: Int, jm: Int, jd: Int): IntArray {
        val jy2 = jy + 1595
        val days = (365 * jy2) + ((jy2 / 33) * 8) + (((jy2 % 33) + 3) / 4) + 78 + jd +
                   if (jm < 7) (jm - 1) * 31 else (jm - 7) * 30 + 186

        var gy = 400 * (days / 146097)
        var days2 = days % 146097

        val temp = days2 / 36524
        gy += 100 * temp
        days2 %= 36524

        val temp2 = days2 / 1461
        gy += 4 * temp2
        days2 %= 1461

        val temp3 = days2 / 365
        if (temp3 > 3) {
            gy += 3
            days2 -= 3 * 365
        } else {
            gy += temp3
            days2 %= 365
        }

        val sal_a = intArrayOf(0, 31, if (isGregorianLeapYear(gy)) 29 else 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31)

        var gm = 0
        while (gm < 13 && days2 >= sal_a[gm]) {
            days2 -= sal_a[gm]
            gm++
        }

        val gd = days2 + 1

        return intArrayOf(gy, gm, gd)
    }

    /**
     * Checks if a Gregorian year is a leap year.
     */
    private fun isGregorianLeapYear(year: Int): Boolean {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
    }

    /**
     * Gets the Persian name of a month.
     * 
     * @param month Month number (1-12)
     * @return Persian month name
     */
    fun getPersianMonthName(month: Int): String {
        return if (month in 1..12) persianMonthNames[month - 1] else ""
    }

    /**
     * Gets the number of days in a Jalali month.
     * 
     * @param year Jalali year
     * @param month Jalali month (1-12)
     * @return Number of days in the month
     */
    fun getDaysInJalaliMonth(year: Int, month: Int): Int {
        return when (month) {
            in 1..6 -> 31
            in 7..11 -> 30
            12 -> if (isJalaliLeapYear(year)) 30 else 29
            else -> 0
        }
    }

    /**
     * Checks if a Jalali year is a leap year.
     * Simple 33-year cycle approximation.
     *
     * @param year Jalali year
     * @return True if leap year, false otherwise
     */
    private fun isJalaliLeapYear(year: Int): Boolean {
        val cycle = year % 128
        val leapYears = intArrayOf(1, 5, 9, 13, 17, 22, 26, 30, 34, 38, 42, 46, 50, 55, 59, 63, 67, 71, 75, 79, 83, 88, 92, 96, 100, 104, 108, 112, 116, 121, 125)
        return leapYears.contains(cycle)
    }

    /**
     * Formats a Jalali date as a string.
     * 
     * @param year Jalali year
     * @param month Jalali month
     * @param day Jalali day
     * @return Formatted date string (YYYY/MM/DD)
     */
    fun formatJalaliDate(year: Int, month: Int, day: Int): String {
        return String.format("%04d/%02d/%02d", year, month, day)
    }
}
