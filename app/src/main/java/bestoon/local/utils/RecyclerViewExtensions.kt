package bestoon.local.utils

import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

/**
 * Extension functions for RecyclerView to simplify common operations.
 */

/**
 * Sets up pagination for a RecyclerView with a LinearLayoutManager.
 * 
 * @param onLoadMore Callback function to load more items
 * @param hasMorePages Function to check if there are more pages available
 * @param isLoadingMore Function to check if currently loading more items
 * @param threshold Number of items from the bottom to trigger loading (default: 3)
 */
fun RecyclerView.setupPagination(
    onLoadMore: () -> Unit,
    hasMorePages: () -> Boolean,
    isLoadingMore: () -> <PERSON><PERSON><PERSON>,
    threshold: Int = 3
) {
    addOnScrollListener(object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)

            val layoutManager = recyclerView.layoutManager as? LinearLayoutManager ?: return
            
            val visibleItemCount = layoutManager.childCount
            val totalItemCount = layoutManager.itemCount
            val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

            // Check if we're near the bottom and should load more
            if (!isLoadingMore() && hasMorePages()) {
                if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - threshold) {
                    onLoadMore()
                }
            }
        }
    })
}
