package bestoon.local.utils

/**
 * Utility class for Persian localization operations.
 */
object PersianUtils {

    /**
     * Converts English numerals to Persian numerals.
     *
     * @param input The string containing English numerals.
     * @return The string with Persian numerals.
     */
    fun convertToPersianNumerals(input: String): String {
        val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
        val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

        var result = input
        for (i in englishDigits.indices) {
            result = result.replace(englishDigits[i], persianDigits[i])
        }
        return result
    }

    /**
     * Converts an integer to Persian numerals.
     *
     * @param number The integer to convert.
     * @return The string with Persian numerals.
     */
    fun convertToPersianNumerals(number: Int): String {
        return convertToPersianNumerals(number.toString())
    }

    /**
     * Converts Persian numerals to English numerals.
     *
     * @param input The string containing Persian numerals.
     * @return The string with English numerals.
     */
    fun convertToEnglishNumerals(input: String): String {
        val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
        val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

        var result = input
        for (i in persianDigits.indices) {
            result = result.replace(persianDigits[i], englishDigits[i])
        }
        return result
    }
}
