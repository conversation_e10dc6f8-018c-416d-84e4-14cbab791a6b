package bestoon.local.utils

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import bestoon.local.BuildConfig
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec

/**
 * Secure credential manager for biometric authentication.
 * Uses Android Keystore to encrypt stored credentials.
 */
class BiometricCredentialManager(context: Context) {

    private val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val keyStore: KeyStore = KeyStore.getInstance(ANDROID_KEYSTORE).apply { load(null) }

    companion object {
        private const val PREF_NAME = "BiometricCredentials"
        private const val KEY_ENCRYPTED_CREDENTIALS = "encrypted_credentials"
        private const val KEY_IV = "encryption_iv"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS = "BiometricCredentialKey"
        private const val TRANSFORMATION = "AES/CBC/PKCS7Padding"
    }

    /**
     * Saves credentials securely using Android Keystore encryption.
     */
    fun saveCredentials(username: String, password: String): Boolean {
        return try {
            // Generate or get encryption key
            val secretKey = getOrCreateSecretKey()
            
            // Encrypt credentials
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val credentials = "$username:$password"
            val encryptedCredentials = cipher.doFinal(credentials.toByteArray())
            val iv = cipher.iv
            
            // Save encrypted data
            val editor = prefs.edit()
            editor.putString(KEY_ENCRYPTED_CREDENTIALS, Base64.encodeToString(encryptedCredentials, Base64.DEFAULT))
            editor.putString(KEY_IV, Base64.encodeToString(iv, Base64.DEFAULT))
            editor.apply()
            
            true
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                android.util.Log.e("BiometricCredentialManager", "Failed to save credentials", e)
            }
            false
        }
    }

    /**
     * Retrieves and decrypts stored credentials.
     */
    fun getCredentials(): Pair<String, String>? {
        return try {
            val encryptedData = prefs.getString(KEY_ENCRYPTED_CREDENTIALS, null) ?: return null
            val ivData = prefs.getString(KEY_IV, null) ?: return null
            
            val secretKey = getOrCreateSecretKey()
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val iv = Base64.decode(ivData, Base64.DEFAULT)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(iv))
            
            val encryptedCredentials = Base64.decode(encryptedData, Base64.DEFAULT)
            val decryptedCredentials = String(cipher.doFinal(encryptedCredentials))
            
            val parts = decryptedCredentials.split(":", limit = 2)
            if (parts.size == 2) {
                Pair(parts[0], parts[1])
            } else {
                null
            }
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                android.util.Log.e("BiometricCredentialManager", "Failed to retrieve credentials", e)
            }
            null
        }
    }

    /**
     * Checks if credentials are stored.
     */
    fun hasCredentials(): Boolean {
        return prefs.contains(KEY_ENCRYPTED_CREDENTIALS) && prefs.contains(KEY_IV)
    }

    /**
     * Clears stored credentials.
     */
    fun clearCredentials() {
        val editor = prefs.edit()
        editor.remove(KEY_ENCRYPTED_CREDENTIALS)
        editor.remove(KEY_IV)
        editor.apply()
        
        // Also remove the key from keystore
        try {
            keyStore.deleteEntry(KEY_ALIAS)
        } catch (e: Exception) {
            if (BuildConfig.DEBUG) {
                android.util.Log.e("BiometricCredentialManager", "Failed to delete key", e)
            }
        }
    }

    /**
     * Gets or creates a secret key for encryption.
     */
    private fun getOrCreateSecretKey(): SecretKey {
        return if (keyStore.containsAlias(KEY_ALIAS)) {
            keyStore.getKey(KEY_ALIAS, null) as SecretKey
        } else {
            generateSecretKey()
        }
    }

    /**
     * Generates a new secret key in Android Keystore.
     */
    private fun generateSecretKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
        val keyGenParameterSpec = KeyGenParameterSpec.Builder(
            KEY_ALIAS,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        )
            .setBlockModes(KeyProperties.BLOCK_MODE_CBC)
            .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_PKCS7)
            .setUserAuthenticationRequired(false) // We handle auth separately with BiometricPrompt
            .build()
        
        keyGenerator.init(keyGenParameterSpec)
        return keyGenerator.generateKey()
    }
}
